from services.external.openai import OpenAIServiceClass
from services.user.user_requisition import UserRequisitionServiceClass
from models.interview_feedback import InterviewFeedback
from models.user_requisition import UserRequisition
import logging
import json

logger = logging.getLogger(__name__)


class PreEvaluate:
    def __init__(self):
        self.openai_service_object = OpenAIServiceClass()

    def evaluate_by_requisition_id(self, requisition_id, db):
        """
        Pre-evaluate candidates for a given requisition ID.

        Args:
            requisition_id (int): The ID of the requisition whose candidates are to be evaluated.
            db (Session): The database session used for querying and updating user requisitions.

        Returns:
            dict: A dictionary containing the total cost of the pre-evaluation.

        Raises:
            Exception: If an error occurs during the pre-evaluation process.
        """

        logger.info(
            f"Pre-evaluating candidates for requisition ID '{requisition_id}'")
        try:
            requisitions = UserRequisitionServiceClass().get_applied_requisitions_id(
                requisition_id, db)

            total_cost = 0
            for requisition in requisitions:
                requisition_id = requisition.requisition_id
                user_id = requisition.user_id

                response = self.openai_service_object.evaluate_candidate_with_resume_requisition(
                    requisition_id, user_id, db)

                total_cost += response['total_cost']

                user_requisition = (
                    db.query(UserRequisition)
                    .filter(
                        UserRequisition.user_id == user_id,
                        UserRequisition.requisition_id == requisition_id,
                        UserRequisition.is_applied == 1,
                    )
                    .first()
                )

                if not user_requisition:
                    user_requisition = UserRequisition(
                        user_id=user_id,
                        requisition_id=requisition_id,
                        status=0,
                        is_saved=0,
                        is_applied=1,
                        score=response["evaluation"]["weighted_percentage"],
                        feedback=json.dumps(response),
                        satisfies_binary_requirements=response['evaluation']['is_satisfactory'],
                    )
                else:
                    user_requisition.score = response["evaluation"][
                        "weighted_percentage"
                    ]
                    user_requisition.feedback = json.dumps(response)
                    user_requisition.satisfies_binary_requirements = response[
                        'evaluation']['is_satisfactory']

                db.add(user_requisition)
                db.commit()
                db.refresh(user_requisition)

            logger.info(
                f"Pre-evaluation completed for requisition ID '{requisition_id}', total cost: ${total_cost}")

            return {'total_cost': total_cost}
        except Exception as e:
            logger.error(
                f"Error pre-evaluating candidates for requisition ID '{requisition_id}': {e}")
            raise e

    def evaluate_candidate(self, requisition_id, request, db):
        """
        Evaluate a candidate against a requisition.

        Args:
            requisition_id (int): The ID of the requisition to evaluate against.
            request (Request): The current request object.
            db (Session): The current database session.

        Returns:
            float: The total cost of the evaluation.

        Raises:
            Exception: If an error occurs during the evaluation process.
        """

        # user_id = request.state.user.id

        # user_requisition = (
        #     db.query(UserRequisition)
        #     .filter(
        #         UserRequisition.user_id == user_id,
        #         UserRequisition.requisition_id == requisition_id,
        #     )
        #     .first()
        # )

        # if not user_requisition:
        #     logger.info("User requisition not found")
        #     return False
        # else:
        evaluation = (
            self.openai_service_object.evaluate_candidate_with_resume_requisition(
                requisition_id, request.state.user.id, db
            )
        )

        #     user_requisition.score = evaluation["evaluation"][
        #         "weighted_percentage"
        #     ]
        #     user_requisition.feedback = json.dumps(evaluation)
        #     user_requisition.satisfies_binary_requirements = evaluation[
        #         'evaluation']['is_satisfactory']

        # db.add(user_requisition)
        # db.commit()
        # db.refresh(user_requisition)

        return evaluation

    def store_candidate_evaluation(self, requisition_id, evaluation, request, db):
        """
        Evaluate a candidate against a requisition.

        Args:
            requisition_id (int): The ID of the requisition to evaluate against.
            request (Request): The current request object.
            db (Session): The current database session.

        Returns:
            float: The total cost of the evaluation.

        Raises:
            Exception: If an error occurs during the evaluation process.
        """

        user_id = request.state.user.id

        user_requisition = (
            db.query(UserRequisition)
            .filter(
                UserRequisition.user_id == user_id,
                UserRequisition.requisition_id == requisition_id,
            )
            .first()
        )

        if not user_requisition:
            logger.info("User requisition not found")
            return False
        else:
            # evaluation = self.openai_service_object.evaluate_candidate_with_resume_requisition(
            #     requisition_id, request.state.user.id, db)

            user_requisition.score = evaluation["evaluation"][
                "weighted_percentage"
            ]
            user_requisition.feedback = json.dumps(evaluation)
            user_requisition.satisfies_binary_requirements = evaluation[
                'evaluation']['is_satisfactory']

        db.add(user_requisition)
        db.commit()
        db.refresh(user_requisition)

        return evaluation

    def store_candidate_opensearch_score(self, requisition_id, evaluation, request, db):
        user_id = request.state.user.id

        user_requisition = (
            db.query(UserRequisition)
            .filter(
                UserRequisition.user_id == user_id,
                UserRequisition.requisition_id == requisition_id,
            )
            .first()
        )

        if not user_requisition:
            logger.info("User requisition not found")
            return False
        else:
            user_requisition.score = round(evaluation["total_score"], 2)
            user_requisition.feedback = json.dumps(evaluation)

        db.add(user_requisition)
        db.commit()
        db.refresh(user_requisition)
        logger.info("Evaluated score updated")
        return user_requisition
