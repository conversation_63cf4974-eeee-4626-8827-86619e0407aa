from models.organization_detail import OrganizationDetail
from models.state import State
from datetime import datetime

from models import Requisition
from services.external.openai import OpenAIServiceClass
import logging
from services.external.opensearch import OpenSearchServiceClass
from dependencies import (
    OPENSEARCH_REQUISITION_EMBEDDING_INDEX,
    OPENSEARCH_RC_INDEX
)
from models.user import User
from models.skill import Skill
from models.user_skill import UserSkill
from models.organization import Organization
from models.user_education import UserEducation
from models.user_experience import UserExperience, UserExperience
from models.school import School
from models.city import City
from models.country import Country
from models.interview_feedback import InterviewFeedback
from dependencies import OPENSEARCH_RC_INDEX


from models.interview_feedback import InterviewFeedback
import json
from datetime import datetime
import re

from models.question_feedback import QuestionFeedback
from models.interview import Interview
from models.user import User
from models.interview_feedback import InterviewFeedback
from models.feedback import Feedback
from services.external.openai import OpenAIServiceClass
from custom_exceptions import EntityNotFoundException
import json
import logging
from datetime import datetime
from helper.helper import (
    convert_utc_to_local,
)
from sqlalchemy import func
from sqlalchemy.dialects.postgresql import <PERSON><PERSON><PERSON>
from services.notification.email import send_email_background
from fastapi.encoders import jsonable_encoder
from schemas.feedback import FeedbackCreateSchema
from custom_exceptions import EntityAlreadyExistsException
from dependencies import OPENSEARCH_RC_INDEX

logger = logging.getLogger(__name__)


class InterviewServiceClass:
    def store_question_feedback(self, question_feedback_data, request, db):
        try:
            question_feedback = (
                db.query(QuestionFeedback)
                .filter(
                    QuestionFeedback.user_id == request.state.user.id,
                    QuestionFeedback.question == question_feedback_data.question,
                    QuestionFeedback.interview_id
                    == question_feedback_data.interview_id,
                )
                .first()
            )
            if not question_feedback:
                question_feedback = QuestionFeedback(
                    user_id=request.state.user.id,
                    interview_id=question_feedback_data.interview_id,
                    question=question_feedback_data.question,
                    feedback=question_feedback_data.feedback,
                    answer=question_feedback_data.answer,
                    duration=question_feedback_data.duration,
                    audio_segment=question_feedback_data.audio_segment,
                    video_link=question_feedback_data.video_link if question_feedback_data.video_link else None,
                    statemachine_node=question_feedback_data.statemachine_node if question_feedback_data.statemachine_node else None,
                    requirement=question_feedback_data.requirement if question_feedback_data.requirement else None
                )
            else:
                question_feedback.feedback = question_feedback_data.feedback
            db.add(question_feedback)
            db.commit()
            db.refresh(question_feedback)
            return question_feedback
        except Exception as e:
            logger.error(f"Error storing question feedback: {e}")
            raise e

    def store_question_feedback_no_auth(self, question_feedback_data, db):
        try:
            logger.info(f"Question feedback data: {question_feedback_data}")
            question_feedback = (
                db.query(QuestionFeedback)
                .filter(
                    QuestionFeedback.user_id == question_feedback_data.user_id,
                    QuestionFeedback.question == question_feedback_data.question,
                    QuestionFeedback.interview_id
                    == question_feedback_data.interview_id
                )
                .first()
            )

            if not question_feedback:
                logger.info("Creating new question feedback")
                question_feedback = QuestionFeedback(
                    user_id=question_feedback_data.user_id,
                    interview_id=question_feedback_data.interview_id,
                    question=question_feedback_data.question,
                    feedback=question_feedback_data.feedback,
                    answer=question_feedback_data.answer,
                    duration=question_feedback_data.duration,
                    audio_segment=question_feedback_data.audio_segment,
                    video_link=question_feedback_data.video_link,
                    statemachine_node=question_feedback_data.statemachine_node,
                    requirement=question_feedback_data.requirement
                )
            else:
                logger.info("Updating existing question feedback")
                question_feedback.feedback = question_feedback_data.feedback
                question_feedback.answer = question_feedback_data.answer
                question_feedback.requirement = question_feedback_data.requirement
                question_feedback.statemachine_node = question_feedback_data.statemachine_node

            db.add(question_feedback)
            db.commit()
            db.refresh(question_feedback)

            logger.info("Question feedback stored successfully")

            return question_feedback
        except Exception as e:
            logger.error(f"Error storing question feedback: {e}")
            raise e

    def update_question_feedback(self, payload, request, db):
        try:
            logger.info("Update Question Feedback started")
            question_feedback = (
                db.query(QuestionFeedback)
                .filter(QuestionFeedback.user_id == request.state.user.id)
                .order_by(QuestionFeedback.id.desc())
                .first()
            )
            if not question_feedback:
                raise EntityNotFoundException("Question feedback not found")

            question_feedback.answer = payload.answer
            question_feedback.duration = payload.duration
            question_feedback.video_link = payload.video_link
            db.commit()
            db.refresh(question_feedback)
            logger.info("Update Question Feedback completed")
            return question_feedback
        except Exception as e:
            logger.error(f"Error updating question feedback {e}")
            raise e

    def get_question_feedback(self, request, db):
        try:
            interview_id = request.query_params.get("interview_id")
            question_feedbacks = (
                db.query(QuestionFeedback)
                .filter(
                    QuestionFeedback.user_id == request.state.user.id,
                    QuestionFeedback.interview_id == interview_id
                )
                .order_by(QuestionFeedback.id.asc())
                .all()
            )
            return jsonable_encoder(question_feedbacks)
        except Exception as e:
            raise e

    def update_interview_status(
        self, interview_id, status, background_tasks, request, db
    ):
        try:
            interview = (
                db.query(Interview)
                .filter(
                    Interview.id == interview_id,
                    Interview.user_id == request.state.user.id,
                )
                .first()
            )
            if not interview:
                raise EntityNotFoundException("Interview not found")
            interview.status = status
            db.commit()
            db.refresh(interview)
            return interview
        except Exception as e:
            raise e

    def get_interview(self, interview_id, request, db):
        try:
            interview = (
                db.query(Interview)
                .filter(
                    Interview.id == interview_id,
                    Interview.user_id == request.state.user.id,
                )
                .first()
            )
            if not interview:
                raise EntityNotFoundException("Interview not found")
            return interview
        except Exception as e:
            raise e

    def get_interview_transcript(self, interview_id, db):
        """
        Get the interview transcript as a string.

        The transcript is a concatenation of all the questions and answers
        with a separator in between.

        Args:
            interview_id (int): The ID of the interview
            db (Session): The database session

        Returns:
            str: The interview transcript
        """

        try:
            # Get all the questions and answers from the database
            question_feedbacks = db.query(QuestionFeedback).filter(
                QuestionFeedback.interview_id == interview_id).order_by(QuestionFeedback.id.asc()).all()

            # Concatenate the questions and answers with a separator
            separator = '\n----\n'
            nl = '\n'
            transcript = separator.join(
                [f"REQUIREMENT: {question_feedback.requirement}{nl}INTERVIEWER: {question_feedback.question}{nl}CANDIDATE: {question_feedback.answer}"
                 for question_feedback in question_feedbacks])

            return transcript
        except Exception as e:
            logger.error("Error fetching interview transcript")
            logger.error(e)
            raise e

    def get_interview_by_id(self, interview_id, db):
        try:
            interview = (
                db.query(Interview)
                .filter(
                    Interview.id == interview_id,
                )
                .first()
            )
            if not interview:
                raise EntityNotFoundException("Interview not found")
            return interview
        except Exception as e:
            raise e

    def get_pending_interviews_by_requisition_id(self, requisition_id, db):
        """
        Retrieve all pending interviews for a given requisition ID.

        This method queries the database for interviews associated with the specified requisition ID and a status of 0, which indicates that the interview is pending. If no interviews are found, an EntityNotFoundException is raised.

        Args:
            requisition_id (int): The ID of the requisition to filter interviews by.
            db (Session): The database session used for querying the interviews.

        Returns:
            list: A list of Interview objects with a pending status.

        Raises:
            EntityNotFoundException: If no interviews are found with the given requisition ID and pending status.
            Exception: If there is an error during the database query.
        """

        try:
            interview = (
                db.query(Interview)
                .filter(
                    Interview.requisition_id == requisition_id,
                    Interview.status == Interview.INTERVIEW_PENDING
                )
                .all()
            )
            if not interview:
                raise EntityNotFoundException("Interviews not found")
            return interview
        except Exception as e:
            raise e

    def get_completed_interviews(self, db, chunk_size: int = None, offset: int = 0) -> list[Interview]:
        """
        Retrieve all completed interviews.

        This method queries the database for completed interviews. If no interviews are found, an EntityNotFoundException is raised.

        Args:
            db (Session): The database session used for querying the interviews.
            chunk_size (int, optional): The maximum number of interviews to retrieve. Defaults to None, which retrieves all entries.
            offset (int, optional): The offset for pagination. Defaults to 0.

        Returns:
            list: A list of completed interview objects.

        Raises:
            EntityNotFoundException: If no interviews are found with the completed status.
            Exception: If there is an error during the database query.
        """

        try:
            interviews = db.query(
                Interview
            ).filter(
                Interview.status == Interview.INTERVIEW_CLOSED
            ).offset(
                offset
            ).limit(
                chunk_size
            ).all()

            return interviews
        except Exception as e:
            raise e

    def store_interview_feedback(self, interview, interview_feedback_data, db):
        try:
            interview_feedback = (
                db.query(InterviewFeedback)
                .filter(
                    InterviewFeedback.user_id == interview.user_id,
                    InterviewFeedback.interview_id == interview.id,
                )
                .first()
            )
            if not interview_feedback:
                interview_feedback = InterviewFeedback(
                    interview_id=interview.id,
                    requisition_id=interview.requisition_id,
                    user_id=interview.user_id,
                    score=interview_feedback_data["evaluation"]["weighted_percentage"],
                    feedback=json.dumps(interview_feedback_data),
                    satisfies_binary_requirements=interview_feedback_data[
                        'evaluation']['is_satisfactory'],
                    location_satisfied=interview_feedback_data['evaluation']['location_satisfied'],
                    status=InterviewFeedback.STATUS_PENDING,
                )
            else:
                interview_feedback.score = interview_feedback_data["evaluation"][
                    "weighted_percentage"
                ]
                interview_feedback.feedback = json.dumps(
                    interview_feedback_data)
                interview_feedback.satisfies_binary_requirements = interview_feedback_data[
                    'evaluation']['is_satisfactory']

            db.add(interview_feedback)
            db.commit()
            db.refresh(interview_feedback)

            return interview_feedback
        except Exception as e:
            raise e

    def generate_interview_feedback(self, interview, db):
        try:
            logger.info("Feedback Job Started")
            openai_service_class = OpenAIServiceClass()
            response = openai_service_class.summarize_interview(interview, db)
            update_interview = db.query(Interview).filter(
                Interview.id == interview.id).first()
            if not response:
                logger.info("Summarize Failed")
                update_interview.status = Interview.INTERVIEW_CLOSE_FAILED
                db.commit()
                return

            interview_feedback = self.store_interview_feedback(
                interview, response, db)

            update_interview.status = Interview.INTERVIEW_CLOSED
            db.commit()

            return interview_feedback
        except Exception as e:
            logger.info("__generate_interview_feedback Failed: ", str(e))
            raise e

    def summarize_interview_questions(self, interview_id, db):
        """
        This method retrieves all question feedbacks for the specified interview, summarizes each question using
        the OpenAI service, and updates the question feedback with the summarized question. If an error occurs 
        during the process, it logs the error and returns False.

        Args:
            interview_id (int): The ID of the interview whose questions are to be summarized.
            db (Session): The database session used for querying and updating question feedbacks.

        Returns:
            bool: True if the summarization process is successful, otherwise False.
        """

        logger.info("Starting summarize_interview_questions")

        try:
            question_feedbacks = db.query(QuestionFeedback).filter(
                QuestionFeedback.interview_id == interview_id).all()
            for question_feedback in question_feedbacks:
                summarized_question = OpenAIServiceClass(
                ).summarize_response(question_feedback.question)

                if summarized_question['hasQuestion']:
                    question_feedback.summarized_question = summarized_question['question']
                else:
                    question_feedback.summarized_question = None

            db.commit()

            logger.info(
                f"Summarize interview questions completed for Interview ID '{interview_id}'")

            return True
        except Exception as e:
            logger.error(e)
            return False

    def update_interview_state_machine_nodes(self, inputData, request, db):
        try:
            print("Updated Interview State Machine Nodes function inittttttttttttttt")
            interview = (
                db.query(Interview)
                .filter(
                    Interview.id == inputData.interview_id,
                    Interview.user_id == request.state.user.id,
                )
                .first()
            )
            if not interview:
                raise EntityNotFoundException("Interview not found")
            interview.state_machine_nodes = inputData.state_machine_nodes
            db.commit()
            db.refresh(interview)
            return jsonable_encoder(interview)
        except Exception as e:
            raise e

    def update_interview_state_machine_nodes_no_auth(self, inputData, db):
        try:
            print(inputData.user_id, inputData.interview_id)
            interview = (
                db.query(Interview)
                .filter(
                    Interview.id == inputData.interview_id,
                    Interview.user_id == inputData.user_id,
                )
                .first()
            )
            if not interview:
                raise EntityNotFoundException("Interview not found")
            interview.state_machine_nodes = inputData.state_machine_nodes
            interview.status = Interview.INTERVIEW_STARTED
            db.commit()
            db.refresh(interview)
            return jsonable_encoder(interview)
        except Exception as e:
            raise e

    def check_interview_plagiarism(self, interview_id, db):
        """
        Check for plagiarism in the interview questions and answers.

        This method retrieves all question feedbacks for the specified interview, checks for plagiarism in each question and answer pair, and updates the question feedback with the plagiarism result. If an error occurs during the process, it logs the error and returns False.

        Args:
            interview_id (int): The ID of the interview to check for plagiarism.
            db (Session): The database session used for querying and updating question feedbacks.

        Returns:
            bool: True if the plagiarism check is successful, otherwise False.
        """

        logger.info("Starting check_interview_plagiarism")

        try:
            interview = db.query(Interview).filter(
                Interview.id == interview_id).first()
            plagiarism_result = OpenAIServiceClass().check_ai_generated_content(
                interview, db
            )
            if not plagiarism_result:
                logger.error("Plagiarism check failed")
                return False

            interview.plagiarism = json.dumps(plagiarism_result)
            db.commit()

            logger.info(
                f"Plagiarism check completed for Interview ID '{interview_id}'")

            return True
        except Exception as e:
            logger.error(e)
            return False

    def store_feedback(self, feedback_data: FeedbackCreateSchema, request, db):
        try:
            user_id = request.state.user.id

            # Check if the interview exists
            interview = db.query(Interview).filter(
                Interview.id == feedback_data.interview_id
            ).first()

            if not interview:
                raise EntityNotFoundException("Interview not found")

            feedback = db.query(Feedback).filter(
                Feedback.interview_id == feedback_data.interview_id,
                Feedback.user_id == user_id
            ).first()
            if feedback:
                raise EntityAlreadyExistsException(
                    "You have already submitted your feedback"
                )                # feedback.rating = feedback_data.rating
                # feedback.comments = feedback_data.comments
                # feedback.interview_id = feedback_data.interview_id
            else:
                feedback = Feedback(
                    interview_id=feedback_data.interview_id,
                    user_id=user_id,
                    rating=feedback_data.rating,
                    comments=feedback_data.comments,
                )
                db.add(feedback)
            db.commit()
            db.refresh(feedback)
            return feedback
        except Exception as e:
            raise e

    def get_feedback(self, interview_id, request, db):
        try:
            user_id = request.state.user.id
            feedback = db.query(Feedback).filter(
                Feedback.interview_id == interview_id,
                Feedback.user_id == user_id
            ).first()
            return feedback
        except Exception as e:
            raise e

    def insert_record_to_opensearch(self, data, db):
        try:
            # Check if data is already inserted
            user_id = data.get("person_id")
            existing_status = (
                db.query(InterviewFeedback.opensearch_status)
                .filter(InterviewFeedback.user_id == user_id)
                .first()
            )

            existing_status = existing_status[0] if existing_status else None
            # By default, in db, we have set the status to 0 for every user. If its 1, it means user has already been inserted
            if existing_status == 1:
                logger.info(
                    f"Data for user {user_id} is already inserted. Skipping.")
                return "Already Inserted"

            # Insert data into OpenSearch
            response = OpenSearchServiceClass(
                index_name=OPENSEARCH_RC_INDEX).insert_to_opensearch_auto_id(data)

            # Update OpenSearch status in the database
            db.query(InterviewFeedback).filter(
                InterviewFeedback.user_id == user_id).update({"opensearch_status": 1})
            db.commit()

            logger.info("Data inserted for user id %s", user_id)
            return response

        except Exception as e:
            logger.error("Error storing embeddings: ", exc_info=True)
            return e

    def update_address_in_opensearch(self, doc_id, address_payload):
        try:
            response = self.opensearch_client.update(
                index=self.index_name,
                id=doc_id,
                body={
                    "doc": {
                        "address": address_payload
                    }
                }
            )
            logger.info(f"Updated address for doc_id={doc_id}")
            return response
        except Exception as e:
            logger.error(f"Failed to update address for doc_id={doc_id}: {e}")

    def calculate_experience_years(self, from_date, to_date=None):
        """Calculate experience in decimal years based on months of difference."""
        if not from_date:
            return 0.0
        if not to_date:
            to_date = datetime.today()

        # Calculate total months difference
        months_diff = (to_date.year - from_date.year) * \
            12 + (to_date.month - from_date.month)
        # If days are involved, adjust months accordingly
        if to_date.day < from_date.day:
            months_diff -= 1  # Round down if the day in to_date is before from_date

        # Convert months to years in decimal
        return round(max(0, months_diff / 12), 2)

    def update_user_opensearch(self, user_id, db):
        # Fetch feedback section only
        feedback_record = db.query(
            InterviewFeedback.interview_id,
            InterviewFeedback.feedback,
            InterviewFeedback.id
        ).filter(InterviewFeedback.user_id == user_id).first()

        feedback_list = []
        feedback_text = "Feedback not available"
        interview_id = None
        interview_feedback_id = None

        if feedback_record:
            interview_id = feedback_record.interview_id
            interview_feedback_id = feedback_record.id
            if feedback_record.feedback:
                try:
                    feedback_data = json.loads(feedback_record.feedback)
                    feedback_list.extend([
                        {"requirement": req.get(
                            "requirement"), "evaluation": req.get("evaluation")}
                        for req in feedback_data.get("requirements", [])
                    ])
                    feedback_text = "\n".join(
                        [f"{fb['evaluation']}" for fb in feedback_list])
                except json.JSONDecodeError:
                    pass

        # Generate new feedback embedding
        feedback_embeddings_result = OpenAIServiceClass().get_embeddings(text=feedback_text)
        feedback_embeddings = feedback_embeddings_result['vector'] if feedback_embeddings_result else None

        # Fetch and process skills
        skill_names = [
            skill.name for skill in db.query(Skill)
            .filter(Skill.id.in_(
                [user_skill.skill_id for user_skill in db.query(UserSkill)
                .filter(UserSkill.user_id == user_id).all()]
            )).all()
        ]

        # Generate skills embeddings
        skills_text = ", ".join(skill_names)
        skills_embeddings_result = OpenAIServiceClass().get_embeddings(text=skills_text.lower())
        skills_embeddings = skills_embeddings_result['vector'] if skills_embeddings_result else None
        # Perform partial update to OpenSearch
        update_payload = {
            "doc": {
                "interview_id": interview_id,
                "interview_feedback_id": interview_feedback_id,
                "feedback": feedback_list,
                "embeddings.feedback": feedback_embeddings,
                "embeddings.skills": skills_embeddings,
                "skills": skill_names
            }
        }

        # Perform update using script or doc update
        opensearch_service = OpenSearchServiceClass(
            index_name=OPENSEARCH_RC_INDEX)
        opensearch_service.update_by_user_id(
            user_id=user_id, update_data=update_payload)
        logger.info("Updated feedback data for existing user")
        return None

    def convert_data(self, user_id, db):
        # Check if the person ID already exists before converting data
        opensearch_service = OpenSearchServiceClass(
            index_name=OPENSEARCH_RC_INDEX)
        exists = opensearch_service.check_id_exists(user_id)

        if exists:
            logger.info(f"User id already exists in opensearch: {user_id}")
            return None

        # Fetch user details
        user = db.query(
            User.id, User.first_name, User.last_name, User.email, User.total_experience, User.city_id, User.created_at, User.location, User.location_preferences, User.expected_salary, User.notice_period
        ).filter(User.id == user_id).first()

        if not user:
            return None

        # Check email presence
        email = user.email if user.email else None
        email_status = 2 if email else 1

        # Fetch skills
        skill_names = [
            skill.name for skill in db.query(Skill)
            .join(UserSkill, Skill.id == UserSkill.skill_id)
            .filter(UserSkill.user_id == user_id)
            .all()
        ]
        skills_text = ", ".join(skill_names)

        # Fetch education records
        education_list = []
        for education in db.query(
            UserEducation.id, UserEducation.degree, UserEducation.field, UserEducation.description,
            UserEducation.start_date, UserEducation.end_date, UserEducation.school_id, UserEducation.city_id
        ).filter(UserEducation.user_id == user.id).all():

            school = db.query(School.name).filter(
                School.id == education.school_id).first()
            # location_parts = education.location.split(",") if education.location else []
            # city = location_parts[0].strip() if len(location_parts) > 0 else None
            # country = location_parts[-1].strip() if len(location_parts) > 1 else None

            education_list.append({
                "degree": education.degree,
                "field": education.field,
                "description": education.description,
                "start_date": education.start_date,
                "end_date": education.end_date,
                "school": school.name if school else None,
                # "city": city,
                # "country": country
            })

        work_experiences = db.query(
                Organization.name.label("organization"),
                OrganizationDetail.description.label("organization_description"),  # Alias for OrganizationDetail.description
                UserExperience.job_title,
                UserExperience.location_type,
                UserExperience.from_date,
                UserExperience.to_date,
                UserExperience.emp_type,
                UserExperience.description.label("experience_description")  # Alias for UserExperience.description
        ).join(
            Organization, UserExperience.organization_id == Organization.id
        ).outerjoin(
            OrganizationDetail, Organization.id == OrganizationDetail.organization_id
        ).filter(
            UserExperience.user_id == user.id
        ).all()

        work_experience_list = []
        for exp in work_experiences:
            work_experience_list.append({
                "organization": exp.organization if exp.organization else "",
                "job_title": exp.job_title if exp.job_title else "",
                "location_type": exp.location_type if exp.location_type else "",
                "from_date": exp.from_date if exp.from_date else None,
                "to_date": exp.to_date if exp.to_date else None,
                "emp_type": exp.emp_type if exp.emp_type else "",
                "description": exp.experience_description if exp.experience_description else "",
                "relevant_experience": self.calculate_experience_years(exp.from_date, exp.to_date),
                "about_company": exp.organization_description if exp.organization_description else "",
            })

        work_experience_text = "\n".join([
            f"Job title: {exp['job_title']}, working at {exp['organization']}. "
            f"Start date is {exp['from_date']} and end date is {exp['to_date']}\n"
            f"Employment type: {exp['emp_type']}, Location: {exp['location_type']}\n"
            f"User experience description: {exp['description']}\n"
            f"Company information: {exp['about_company']}"
            for exp in work_experience_list
        ])

        # # Fetch the first feedback entry
        # feedback_record = db.query(InterviewFeedback.interview_id, InterviewFeedback.feedback, InterviewFeedback.id)\
        #     .filter(InterviewFeedback.user_id == user.id).first()

        # Fetch the first feedback entry (include created_at)
        feedback_record = db.query(
            InterviewFeedback.interview_id,
            InterviewFeedback.feedback,
            InterviewFeedback.id
        ).filter(InterviewFeedback.user_id == user.id).first()

        feedback_list = []
        interview_id = None  # Default to None if no feedback exists
        interview_feedback_id = None

        if feedback_record:
            interview_id = feedback_record.interview_id  # Capture the interview ID
            interview_feedback_id = feedback_record.id
            if feedback_record.feedback:
                try:
                    feedback_data = json.loads(feedback_record.feedback)
                    feedback_list.extend([
                        {"requirement": req.get(
                            "requirement"), "evaluation": req.get("evaluation")}
                        for req in feedback_data.get("requirements", [])
                    ])
                    feedback_text = "\n".join(
                        [f"{fb['evaluation']}" for fb in feedback_list])
                except json.JSONDecodeError:
                    pass  # Skip invalid JSON

        # If feedback is empty, set "Feedback not available"
        if not feedback_list:
            logger.info(
                f"No feedback found for user: {user_id}, setting 'Feedback not available'")
            feedback_text = "Feedback not available"

        # Generate embeddings
        feedback_embeddings_result = OpenAIServiceClass(
        ).get_embeddings(text=feedback_text.lower())
        feedback_embeddings = feedback_embeddings_result['vector'] if feedback_embeddings_result else None

        work_experience_embeddings_result = OpenAIServiceClass(
        ).get_embeddings(text=work_experience_text.lower())
        work_experience_embeddings = work_experience_embeddings_result[
            'vector'] if work_experience_embeddings_result else None

        skills_embeddings_result = OpenAIServiceClass(
        ).get_embeddings(text=skills_text.lower())
        skills_embeddings = skills_embeddings_result['vector'] if skills_embeddings_result else None

        # Function to parse total experience string

        def parse_total_experience(exp_str):
            if not exp_str or exp_str == "_":
                return 0
            match = re.match(
                r"(\d+)(?:\+)?(?:\s*years)?(?:\s*(\d+)\s*months)?", exp_str)
            if match:
                years = int(match.group(1))
                months = int(match.group(2)) if match.group(2) else 0
                total_years = years + months / 12.0
                # This will round 2.5 to 3, 2.4 to 2, etc.
                return round(total_years)
            return 0

        total_experience = parse_total_experience(user.total_experience)

        # logger.info({"city": db.query(City.name).filter(City.id == user.city_id).first() if user.city_id else None,
        #         "country": db.query(Country.name)
        #         .filter(Country.id == db.query(City.country_id).filter(City.id == user.city_id).scalar())
        #         .first() if user.city_id else None})
        # city = db.query(City.name).filter(
        #     City.id == user.city_id).first() if user.city_id else None
        # country = None
        # if user.city_id:
        #     city_country_id = db.query(City.country_id).filter(
        #         City.id == user.city_id).scalar()
        #     if city_country_id:
        #         country_obj = db.query(Country.name).filter(
        #             Country.id == city_country_id).first()
        #         country = country_obj.name if country_obj else None

        # location_parts = user.location.split(",") if user.location else []
        # city = location_parts[0].strip() if len(location_parts) > 0 else None
        # country = location_parts[-1].strip() if len(location_parts) > 1 else None

        address = self.get_address_from_postgres(user_id=user_id, db=db)
        if address is not None:
            address= address['address']
        
        created_at = user.created_at
        created_at_str = created_at.strftime(
            '%Y-%m-%dT%H:%M:%S.%f')[:-3] if created_at else None

        return {
            "person_id": user.id,
            "source": "postgres",
            "interview_id": interview_id,
            "interview_feedback_id": interview_feedback_id,
            "created_at": created_at_str,
            "name": f"{user.first_name} {user.last_name}",
            "email": email,
            "email_status": email_status,
            "expected_salary": user.expected_salary,
            "notice_period": user.notice_period,
            "location_preferences": user.location_preferences.replace("-", "") if user.location_preferences else "",
            "total_experience": total_experience,
            "skills": skill_names,
            "certifications": [],
            "linkedin": "",
            "summary": "",
            "education": education_list,
            "feedback": feedback_list,
            "work_experience": work_experience_list,
            "address": address,
            "embeddings": {
                "skills": skills_embeddings,
                "work_experience": work_experience_embeddings,
                "feedback": feedback_embeddings
            }
        }

    def get_address_from_postgres(self, user_id, db):

        # Fetch user details
        user = db.query(
            User.id, User.first_name, User.last_name, User.email, User.total_experience, User.city_id, User.created_at, User.location, User.country_id, User.state_id, User.location_preferences, User.notice_period, User.expected_salary
        ).filter(User.id == user_id).first()

        if not user:
            return None
        city_name, city_id = "", 0
        country_name, country_id = "", 0
        state_name, state_id = "", 0

        if user.city_id and user.city_id > 0:
            city_object = db.query(City).filter(
                City.id == user.city_id).first()
            if city_object:
                city_name = city_object.name
                city_id = city_object.id

        if user.country_id and user.country_id > 0:
            country_object = db.query(Country).filter(
                Country.id == user.country_id).first()
            if country_object:
                country_name = country_object.name
                country_id = country_object.id

        if user.state_id and user.state_id > 0:
            state_object = db.query(State).filter(
                State.id == user.state_id).first()
            if state_object:
                state_name = state_object.name
                state_id = state_object.id

        location = user.location if user.location else ""

        # logger.info(f"City: {city_name}, Country: {country_name}, State: {state_name} for user_id={user.id}")

        return {
            "person_id": user.id,
            "address": {
                "city": city_name,
                "country": country_name,
                "state": state_name,
                "city_id": city_id,
                "state_id": state_id,
                "country_id": country_id,
                "location": location,
                "street": "",
                "zip": ""
            },
            "expected_salary":user.expected_salary,
            "location_preferences": user.location_preferences,
            "notice_period":user.notice_period
        }

    def bulk_insert_to_opensearch(self, db):
        # Fetch all user IDs present in the InterviewFeedback table
        user_ids = (
            db.query(User.id)
            .filter(
            User.id.in_(db.query(InterviewFeedback.user_id)
                    .filter(InterviewFeedback.opensearch_status == 0)
                    .distinct())
            )
            .all()
        )

        user_ids = [user.id for user in user_ids]  # Extract IDs from tuples

        if not user_ids:
            logger.info("No users found with interview feedback.")
            return []

        logger.info(f"Processing {len(user_ids)} users for conversion.")

        converted_data = []
        opensearch_service = OpenSearchServiceClass(
            index_name=OPENSEARCH_RC_INDEX)

        for user_id in user_ids:
            try:
                user_data = self.convert_data(user_id, db)
                if user_data:
                    self.insert_record_to_opensearch(user_data, db)
                    db.query(InterviewFeedback).filter(
                        InterviewFeedback.user_id == user_id
                    ).update({"opensearch_status": 1}, synchronize_session=False)
                    db.commit()
                    converted_data.append(user_data)
            except Exception as e:
                logger.error(f"Error processing user_id={user_id}: {e}")
                db.rollback()
        logger.info(f"Successfully converted {len(converted_data)} users.")
        return converted_data

    def bulk_update_to_opensearch(self, db):
        # Fetch all user IDs present in the InterviewFeedback table
        user_ids = (
            db.query(User.id)
            .filter(User.id.in_(db.query(InterviewFeedback.user_id).distinct()))
            .all()
        )

        user_ids = [user.id for user in user_ids]  # Extract IDs from tuples

        if not user_ids:
            logger.info("No users found with interview feedback.")
            return []

        logger.info(f"Processing {len(user_ids)} users for conversion.")

        converted_data = []
        opensearch_service = OpenSearchServiceClass(index_name=OPENSEARCH_RC_INDEX)

        for user_id in user_ids:
            try:
                user_data = self.get_address_from_postgres(user_id, db)

                if user_data and "person_id" in user_data:
                    doc_id = user_data.get("person_id")

                    # Prepare update payload with all four keys if available
                    update_payload = {}
                    for key in ["address", "location_preferences", "notice_period", "expected_salary"]:
                        if key in user_data:
                            update_payload[key] = user_data[key]

                    # Update if there's anything to send
                    if update_payload:
                        opensearch_service.update_address_in_opensearch(doc_id, update_payload)
                        converted_data.append(user_data)

            except Exception as e:
                logger.error(f"Error processing user_id={user_id}: {e}")
                db.rollback()

        return converted_data



    def bulk_insert_to_opensearch_users_table(self, db, batch_size=100):
        logger.info("Starting background process...")

        offset = 0
        converted_data = []
        opensearch_service = OpenSearchServiceClass(
            index_name=OPENSEARCH_RC_INDEX)

        while True:
            users = db.query(User).offset(offset).limit(batch_size).all()
            if not users:
                break

            logger.info(f"Processing {len(users)} users from offset {offset}")

            for user in users:
                try:
                    user_data = self.convert_data(user.id, db)
                    if user_data:
                        self.insert_record_to_opensearch(user_data, db)
                        db.query(User).filter(User.id == user.id).update(
                            {"opensearch_status": 1}, synchronize_session=False
                        )
                        db.commit()
                        converted_data.append(user_data)
                except Exception as e:
                    logger.error(f"Error processing user_id={user.id}: {e}")
                    db.rollback()

            offset += batch_size

        logger.info(f"Successfully converted {len(converted_data)} users.")
        return converted_data

    def insert_users_to_opensearch(self, db):
        """
        Inserts users into OpenSearch who meet specific criteria and are not present in the 
        interview feedback records.

        Args:
            db (Session): The database session object used to query the database.

        Raises:
            Exception: Propagates any exceptions encountered during the operation.

        Query Filters:
            - Users who are not present in the `InterviewFeedback` table.
            - Users whose `opensearch_status` is 0.
            - Users who are not guests (`is_guest` is False).
            - Users whose `resume_link` is a valid JSON object containing a non-empty `file_path`.
        """

        try:
            query = (
                db.query(User.id)
                .outerjoin(InterviewFeedback, InterviewFeedback.user_id == User.id)
                .filter(
                    InterviewFeedback.id == None,
                    User.opensearch_status == 0,
                    User.is_guest == False,
                    # crude check for valid JSON object
                    User.resume_link.like('{%'),
                    func.coalesce(
                        func.cast(User.resume_link, JSON)[
                            'file_path'].astext, ''
                    ) != ''
                )
            )

            for user in query:
                logger.info(f"Inserting user {user[0]} to opensearch.")

                user_data = self.convert_data(user[0], db)
                if user_data:
                    self.insert_record_to_opensearch(user_data, db)
                    updated_row = db.query(User).filter(User.id == user.id).update(
                        {"opensearch_status": 1}
                    )
                    db.commit()

                    if updated_row:
                        logger.info(
                            f"User {user[0]} inserted and updated successfully.")
                    else:
                        logger.info(
                            f"User {user[0]} not updated in the database.")
            return True

        except Exception as e:
            logger.error(f"Error in insert_users_to_opensearch: {e}")
            raise e
