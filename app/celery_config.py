# import celery_tasks.interview_email_tasks
from dependencies import (
    <PERSON><PERSON>ER_HOST,
    <PERSON><PERSON>ER_PORT,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ER_PASS,
    ENVIRONMENT,
    BR<PERSON>ER_USER,
)
from celery import Celery
from celery.schedules import crontab
from kombu import Queue, Exchange

# print(f"{BR<PERSON><PERSON>}://:{BROKER_PASS}@{BROKER_HOST}:{BROKER_PORT}")
# redis://:password@hostname:port/db_number

broker_url = f"{BROKER}://{BROKER_HOST}:{BROKER_PORT}" if ENVIRONMENT == "local" else f"{BROKER}://:{BROKER_PASS}@{BROKER_HOST}:{BROKER_PORT}"
# broker_url = f"{BROKER}://{BROKER_USER}:{BROKER_PASS}@{BROKER_HOST}:{BROKER_PORT}"
print("gg", broker_url)


celery_app = Celery(
    __name__,
    broker=broker_url,
    backend="rpc://",
    include=[
        "services.requisition.recommendation",
        "celery_tasks.interview_email_tasks",
        "celery_tasks.post_interview_process_tasks",
        "celery_tasks.onboarding_tasks",
        "celery_tasks.scrapping_tasks",
        "celery_tasks.post_requisition_process_tasks",
        "celery_tasks.clay_requisition_tasks",
        "celery_tasks.interview_prep",
        "celery_tasks.send_interview_invite",
        "celery_tasks.upload_resume",
        "celery_tasks.migration_opensearch",
        "celery_tasks.enrich_company_info",
        "celery_tasks.export_user_links",
        "celery_tasks.qc_automation",
        "celery_tasks.recruiter_conversation",
        "celery_tasks.candidate_tasks",
    ],
)

# Define exchanges
default_exchange = Exchange("default", type="direct")
interview_post_process = Exchange("post_interview_process_tasks", type="direct")
scrapping_task = Exchange("scrapping_tasks", type="direct")
onboarding_tasks = Exchange("onboarding_tasks", type="direct")
post_requisition_process_tasks = Exchange("post_requisition_process_tasks", type="direct")
clay_requisition_tasks = Exchange("clay_requisition_tasks", type="direct")
send_interview_invite = Exchange("send_interview_invite", type="direct")
enrich_company_info = Exchange("enrich_company_info", type="direct")
enrichment_and_insertion_exchange = Exchange("enrichment_and_insertion", type="direct")
recruiter_conversation = Exchange("recruiter_conversation", type="direct")


# Define queues
TASK_QUEUES = (
    Queue("default", default_exchange, routing_key="default"),
    Queue(
        "interview_post_process_queue",
        interview_post_process,
        routing_key="post_interview_process.#",
    ),
    Queue(
        "scrapping_task_queue",
        scrapping_task,
        routing_key="scrapping_task.#",
    ),
    Queue("send_recruiter_outreach_email_queue",
           default_exchange, 
           routing_key="send_recruiter_outreach_email"),
    Queue(
        "post_requisition_process_tasks_queue",
        post_requisition_process_tasks,
        routing_key="post_requisition_process_tasks.#",
    ),
    Queue(
        "recruiter_requsition_conversation_queue",
        recruiter_conversation,
        routing_key="recruiter_conversation"
),
    Queue(
        "clay_requisition_tasks_queue",
        clay_requisition_tasks,
        routing_key="clay_requisition_tasks.#",
    ),
    Queue(
        "interview_invite_queue",
        send_interview_invite,
        routing_key="send_interview_invite",
    ),
    Queue(
    "bulk_queue",
    enrich_company_info,
    routing_key="enrich_company_info",
    ),
    Queue(
        "onboarding_queue",
        onboarding_tasks,
        routing_key="onboarding_tasks.#",
    ),
    Queue(
        "enrichment_and_insertion",
        enrichment_and_insertion_exchange,
        routing_key="enrichment_and_insertion",
    ),
)


# Routes
TASK_ROUTES = {
    "celery_task.post_interview_process.*": {
        "queue": "interview_post_process_queue",
        "routing_key": "post_interview_process",
    },
    "celery_task.scrapping_task.*": {
        "queue": "scrapping_task_queue",
        "routing_key": "scrapping_task",
    },
    "celery_task.send_recruiter_outreach_email.*": {
        "queue": "send_recruiter_outreach_email_queue",
        "routing_key": "send_recruiter_outreach_email"
    },
    "celery_task.post_requisition_process_tasks.*": {
        "queue": "post_requisition_process_tasks_queue",
        "routing_key": "post_requisition_process_tasks",
    },
    "celery_task.clay_requisition_tasks.*": {
        "queue": "clay_requisition_tasks_queue",
        "routing_key": "clay_requisition_tasks",
    },
    "celery_tasks.send_interview_invite.*": {
        "queue": "interview_invite_queue",
        "routing_key": "send_interview_invite",
    },
    "celery_tasks.export_user_links.*": {
        "queue": "interview_invite_queue",
        "routing_key": "send_interview_invite",
    },
    "celery_tasks.qc_automation.*": {
        "queue": "interview_invite_queue",
        "routing_key": "send_interview_invite",
    },
    "celery_tasks.recruiter_conversation.*": {
        "queue": "recruiter_requsition_conversation_queue",
        "routing_key": "recruiter_conversation",
    },
    "celery_tasks.onboarding_tasks.*": {
        "queue": "onboarding_queue",
        "routing_key": "onboarding_tasks",
    },
    "celery_tasks.enrich_company_info.enrich-company-info": {
        "queue": "enrichment_and_insertion",
        "routing_key": "enrichment_and_insertion",
    },
    "celery_tasks.candidate_tasks.evaluate_candidate": {
        "queue": "enrichment_and_insertion",
        "routing_key": "enrichment_and_insertion",
    },
    "celery_tasks.enrich_company_info.*": {
        "queue": "bulk_queue",
        "routing_key": "enrich_company_info",
    },
    "celery_tasks.migration_opensearch.education-update-opensearch": {
        "queue": "bulk_queue",
        "routing_key": "enrich_company_info",
    },
    "celery_tasks.migration_opensearch.education-update-batch": {
        "queue": "bulk_queue",
        "routing_key": "enrich_company_info",
    },
    "*": {
        "queue": "default",
        "routing_key": "default",
    },
}

celery_app.conf.beat_schedule = {
    "half-hour": {
        "task": "send-half-hour-reminder-email",
        "schedule": crontab(minute="*/5"),
    },
    "day-end": {
        "task": "missed-interview-email",
        "schedule": crontab(minute=10, hour="*"),
        # crontab(minute='*/30')
    },
    "day-start": {
        "task": "send-today-reminder-email",
        "schedule": crontab(minute=0, hour="*/1"),
    },
    "scrapping-dog-jobs-daily": {
        "task": "scrapping-dog-jobs-to-mongo",
        "schedule": crontab(hour=0, minute=0),  # Runs at midnight (00:00)
    },
    "prev-day": {
        "task": "previous-day-reminder-email",
        "schedule": crontab(minute=5, hour="*"),
        # crontab(minute='*/30')
    },

}

# Ensure Celery uses the same logging configuration
celery_app.conf.update(
    task_queues=TASK_QUEUES,
    task_routes=TASK_ROUTES,
    task_default_queue='default',
    task_default_exchange='default',
    task_default_exchange_type='direct',
    task_default_routing_key='default',
    broker_connection_retry_on_startup=True,
    worker_log_format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    worker_task_log_format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    timezone='UTC',
    enable_utc=True,
    result_expires=7200, # 2 hours
)
