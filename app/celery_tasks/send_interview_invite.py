from services.external.bedrock import BedrockService
from fastapi import Request, HTTPException, BackgroundTasks
from celery_tasks.enrich_company_info import enrich_company_info
from pprint import pprint
from celery_config import celery_app
import json
import logging
import time
from models.user import User
from models.user_requisition import UserRequisition
from models.country import Country
from database import sessionLocal
from sqlalchemy.orm import Session
from dependencies import (
    APP_HOST,
    USER_INVITE_URL,
    AWS_AUTOMATION_RESUME_FOLDER,
    AWS_BUCKET,
    AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY,
    AWS_DEFAULT_REGION,
    CANDIDATE_THRESHOLD_SCORE,
    RESUME_THRESHOLD_SCORE,
    AWS_RESUME_FOLDER,
    INTERVIEW_SENDER_EMAIL
)
from services.filemanager.filemanager import FileManagerClass
from services.user.user import UserServiceClass
from services.parser.text_extractor_service import TextExtractorService
from services.external.openai import OpenAIServiceClass
from services.user.user_requisition import UserRequisitionServiceClass
from services.requisition.requisition import RequisitionService<PERSON>lass
from helper.helper import get_user_education_object, get_user_experience_object
import os
from services.auth import AuthServiceClass
from services.notification.email import send_email_background, send_email_with_celery
from services.user.user import UserServiceClass
import requests
import uuid
from datetime import datetime
from models.failed_invite import FailedInvite
from database import sessionLocal, get_mongodb_client
from services.interview.pre_evaluate import PreEvaluate
from services.change_preeval_algo import PreEvalServiceClass
from services.location_service import LocationService
import csv
import os
from services.interview.interview import InterviewServiceClass
from models.interview_feedback import InterviewFeedback
from services.external.opensearch import OpenSearchServiceClass
from services.qc_automation.qc_automation import QCAutomationClass
from dependencies import OPENSEARCH_RC_INDEX, OPENSEARCH_REQUISITION_EMBEDDING_INDEX
import numpy as np
from services.requisition.requisition_similarity import RequisitionSimilarity

logger = logging.getLogger(__name__)

file_manager_object = FileManagerClass()
user_requisition_service_object = UserRequisitionServiceClass()
text_extractor_service = TextExtractorService()
user_service_object = UserServiceClass()
open_ai_service_object = OpenAIServiceClass()
auth_service_object = AuthServiceClass()
pre_eval = PreEvalServiceClass()
interview_service = InterviewServiceClass()
qc_automation_service = QCAutomationClass()
# mobile_wallet_requisition_id,
#                     IAM_requisition_id,
#                     title,
#                     resume,
#                     first_name,
#                     last_name,
#                     db,
#                     email,
#                     number,
#                     linkdin_url,
#                 )

location_service_object = LocationService()


@celery_app.task(name="celery_tasks.send_interview_invite.JD_matching")
def JD_matching(
    mobile_wallet_requisition_id,
    IAM_requisition_id,
    title,
    resume,
    first_name,
    last_name,
    email,
    number,
    linkedin_url,
):
    db: Session = sessionLocal()
    logger.info(f"Automation Process Started")
    mock_scope = {
        "type": "http",
        "method": "GET",
        "headers": [],
    }
    request = Request(scope=mock_scope)

    try:
        resume_file = get_resume(resume, title)
        logger.info(f"Resume file: {resume_file}")
        llm_response = JD_matching_upload_resume(resume_file, db)
        logger.info(f"LLM Response: {llm_response}")
        pre_eval.convert_resume_to_opensearch_format_and_insert(
            llm_response, email)

        score_mobile = pre_eval.get_similarity(
            mobile_wallet_requisition_id, email)
        score_iam = pre_eval.get_similarity(IAM_requisition_id, email)

        # Log the scores
        logger.info(
            f"Score for Mobile Wallet Requisition ({mobile_wallet_requisition_id}): {score_mobile}")
        logger.info(
            f"Score for IAM Requisition ({IAM_requisition_id}): {score_iam}")

        final_requisition_id = mobile_wallet_requisition_id if score_mobile >= score_iam else IAM_requisition_id
        logger.info(f"Selected Final Requisition ID: {final_requisition_id}")

        log_to_csv(first_name, last_name, email, number,
                   resume_file, linkedin_url, final_requisition_id)

        logger.info(f"Automation Process Completed")
    except Exception as e:
        logger.error(f"Error processing JD matching: {str(e)}")
        return False
    finally:
        logger.info(f"Job processing completed for field")
        db.close()
        return True


def log_to_csv(first_name, last_name, email, number, resume_filename, profile_url, final_req_id, csv_path='jd_matching_log.csv'):
    file_exists = os.path.isfile(csv_path)

    with open(csv_path, mode='a', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)

        # Write blank top row and headers if file doesn't exist yet
        if not file_exists:
            writer.writerow([""])
            writer.writerow(["", "First Name", "Last Name", "Email",
                            "Number", "Name of Resume File", "Profile URL", "Req ID"])

        writer.writerow(["", first_name, last_name, email,
                        number, resume_filename, profile_url, final_req_id])


@celery_app.task(name="celery_tasks.send_interview_invite.send_invite")
def send_invite(
    requsition_id,
    title,
    first_name,
    last_name,
    email,
    number,
    resume,
    linkdin_url,
    version,
    pre_evaluation=False,
):
    logger.info(f"Automation Process Started")
    db: Session = sessionLocal()
    background_tasks = BackgroundTasks()
    # Mock scope for the Request object
    mock_scope = {
        "type": "http",
        "method": "GET",
        "headers": [],
    }
    request = Request(scope=mock_scope)

    try:
        # Change email to lowercase
        email = email.lower()
        # check if user already exists
        existing_user = db.query(User).filter(User.email == email).first()
        if not existing_user:
            logger.info(f"User not found with email: {email}")
            user = register_user(first_name, last_name,
                                 email, number, linkdin_url, db)
            request.state.user = user
            # ### resume upload work
            is_resume_upload = check_and_upload_resume(user, resume, title, db, request)
            if is_resume_upload:
                upload_user_into_opensearch(user, db)

        elif existing_user.resume_link is None or not existing_user.resume_link:
            logger.info(f"User resume link not found: {email}")
            request.state.user = existing_user
            is_resume_upload = check_and_upload_resume(
                existing_user, resume, title, db, request)
            if is_resume_upload:
                upload_user_into_opensearch(existing_user, db)

        else:
            logger.info(f"User found with email: {email}")
            request.state.user = existing_user

        # check if user has applied to any requisition in past
        check_already_applied_to_any_requisition = (
            db.query(UserRequisition)
            .filter(
                UserRequisition.user_id == request.state.user.id,
                UserRequisition.is_applied == 1,
            )
            .first()
        )
        if check_already_applied_to_any_requisition:
            logger.info(
                f"User already applied to a requisition with id: {check_already_applied_to_any_requisition.requisition_id}"
            )
            add_failed_invite(
                "Already Applied",
                {
                    "message": f"User already applied to a requisition: {check_already_applied_to_any_requisition.requisition_id}"
                },
                0,
                request.state.user.id,
                db,
            )
            return False
        else:
            logger.info(f"User not applied to any requisition")
            apply_req(request, db, requsition_id, version, background_tasks)
            if pre_evaluation:
                logger.info(f"Pre-evaluation started")
                #Get Requisition Embeddings
                requisition_embeddings = OpenSearchServiceClass(
                        OPENSEARCH_REQUISITION_EMBEDDING_INDEX
                    ).get_single_document(requsition_id)
                
                #Safe check for requisition embeddings
                if not requisition_embeddings:
                    logger.info(f"Requisition embeddings not found, storing embeddings")
                    RequisitionSimilarity().store_embeddings(requsition_id, db)
                    requisition_embeddings = OpenSearchServiceClass(
                        OPENSEARCH_REQUISITION_EMBEDDING_INDEX
                    ).get_single_document(requsition_id)
                    
                #Check if user is in opensearch
                if request.state.user.opensearch_status == 1:
                    evaluation_result = OpenSearchServiceClass(OPENSEARCH_RC_INDEX).get_user_resume_score(
                        np.array(requisition_embeddings["embeddings"]),
                        request.state.user.id
                    )
                    logger.info(f"Evaluation Result: {evaluation_result}")
                    evaluation_result_store = PreEvaluate().store_candidate_opensearch_score(
                        requsition_id, evaluation_result[request.state.user.id], request, db
                    )
                else:
                    logger.info(f"User not in opensearch with ID: {request.state.user.id}")
                # Not using the old pre-evaluation code which utilize the LLM
                # evaluation_result = PreEvaluate().evaluate_candidate(
                #     requsition_id, request, db
                # )

        logger.info(f"Automation Process Completed")
    except Exception as e:
        logger.error(f"Error processing {email}: {str(e)}")
        if request.state.user.id:  # Only add failed invite if we have a user_id
            add_failed_invite(
                "Processing Error",
                {"message": f"Error Processing Invite: {str(e)}"},
                0,
                request.state.user.id,
                db,
            )
        raise
    finally:
        logger.info(f"Job processing completed for field: {email}")
        db.close()
        return True


def apply_req(request, db, requsition_id, version, background_tasks):
    try:
        logger.info(f"Apply Requisition Started on version: {version}")
        response = user_requisition_service_object.take_action_on_requisition(
            "apply", requsition_id, request, db, version
        )
        requisition = RequisitionServiceClass().get_requisition(
            request, requsition_id, db
        )
        # Create access token to verify user
        secret_token = auth_service_object.create_access_token(
            request.state.user.email
        )
        # Email Body
        redirect_link = f"{APP_HOST}{USER_INVITE_URL}?access_token={secret_token}&redirect_to=schedule-interview"
        email_body = {
            "name": f"{request.state.user.first_name} {request.state.user.last_name}",
            "redirect_link": redirect_link,
            "requisition_title": requisition['title'],
            "organization_name": requisition["organization"]["name"],
        }
        print(email_body)
        logger.info(str(email_body))
        send_email_with_celery(
            "Next Step: Schedule Your Interview!",
            request.state.user.email,
            email_body,
            "apply_to_requsition.html",
            INTERVIEW_SENDER_EMAIL
        )
        logger.info(f"Apply Requisition Completed")
        return response
    except Exception as e:
        logger.error(f"Error in apply_req: {e}")
        return False


def JD_matching_upload_resume(resume_file, db):
    try:
        logger.info(f"Upload Resume Service Started")
        response = requests.get(resume_file)
        if response.status_code != 200:
            logger.error(
                f"Failed to download file from S3 URL: {resume_file}")
            return {"error": f"Failed to download file. Status code: {response.status_code}"}
        temp_file_path = str(uuid.uuid4())+".pdf"
        with open(temp_file_path, 'wb') as temp_file:
            temp_file.write(response.content)
            logger.info(f"Upload Resume Service Started")

        if not resume_file:
            logger.info(f"File path is not valid")

        parser_text = text_extractor_service.extract_text(temp_file_path)
        if not parser_text:
            logger.info(f"Parser text is not valid")

        # Use OpenAI to extract fields from the text
        open_ai_service_object = OpenAIServiceClass()
        
        llmResponse = open_ai_service_object.extract_fields_from_text(
            parser_text)
        return llmResponse
    except Exception as e:
        logger.error("Error in uploading resume")
        logger.error(str(e))
    finally:
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)


def upload_resume(request, resume, file_url, resume_file, db, user_id):
    try:
        logger.info(f"Upload Resume Service Started")
        response = requests.get(resume_file)
        if response.status_code != 200:
            logger.error(
                f"Failed to download file from S3 URL: {resume_file}")
            return {"error": f"Failed to download file. Status code: {response.status_code}"}
        temp_file_path = str(uuid.uuid4())+".pdf"
        with open(temp_file_path, 'wb') as temp_file:
            temp_file.write(response.content)
            logger.info(f"Upload Resume Service Started")

        # logger.info(f"File Path {file_path}")

        if not resume_file:
            logger.info(f"File path is not valid")

        parser_text = text_extractor_service.extract_text(temp_file_path)
        
        llmResponse=None
        # Check if parser text is valid
        if not parser_text:
            logger.info("Parser text is invalid, scanning resume now...")
            llmResponse = qc_automation_service.scan_resume_and_extract_data(file_url)
            llmResponse=llmResponse['response']
        
        else:
            logger.info("Parser text valid...")
            llmResponse = BedrockService().analyze_resume_from_text(parser_text)
        
        logger.info(f"LLM Response Completed")
        total_experience = llmResponse["total_experience"]
        city = llmResponse.get("address", {}).get("city", None)
        country_code = llmResponse.get("address", {}).get("country", None)
        state= llmResponse.get("address", {}).get("state", None)
        logger.info("LLM Response Fetched")
        # update user education data
        user_education_object = get_user_education_object(
            llmResponse["education"], db
        )
        logger.info("User Education Object Fetched")
        update_education = user_service_object.update_education(
            user_education_object, request, db
        )
        logger.info("User Education Updated")
        # update user experience
        user_experience_object = get_user_experience_object(
            llmResponse["work_experience"], db
        )
        logger.info("User Experience Object Fetched")
        user_experience = user_service_object.update_experience(
            user_experience_object, request, db
        )
        logger.info("User Experience Updated")
        # update user skills
        skill = user_service_object.create_update_user_skill(
            llmResponse["skills"], request, db
        )


        data=location_service_object.get_location(city, country_code, state, db)


        hash_key = (
            str(user_id)
            + "-"
            + str(resume)
            + "-"
            + str(datetime.now())
            + "-"
            + "resume"
        )
        file_hash = file_manager_object.__create_file_hash__(hash_key)
        _, file_extension = os.path.splitext(resume)
        file_hash_with_extension = file_hash + file_extension
        file_name_object = {
            "file_name": file_hash_with_extension,
            "file_path": file_url,
        }
        user_object_to_update = {
            "resume_link": json.dumps(file_name_object),
            "total_experience": total_experience,
            "city_id": data["city_id"],
            "country_id": data["country_id"],
            "state_id": data["state_id"],
            "location": data["location"],
            "about": llmResponse["summary"],
            "linkedin": str(llmResponse.get('linkedin', None))
        }

        # update user with resume link
        user_object = user_service_object.update_user_resume_detail(
            user_object_to_update, request, db
        )

        enrich_company_info.delay(request.state.user.id)        
        # delete file in temp folder
        # file_manager_object.delete_temp_file(file_path)
        logger.info("Upload Resume Service Completed")
        return True

    except Exception as e:
        logger.error("Error in uploading resume")
        logger.error(str(e))
        return False       
    finally:
        # Clean up the temporary file if it exists
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)


def register_user(first_name, last_name, email, number, linkdin_url, db):
    try:
        logger.info(f"Register User Started with email: {email}")
        password = "snabup@123"
        hashed_password = UserServiceClass().hash_password(password)
        user = User(
            first_name=first_name,
            last_name=last_name,
            phone=number,
            email=email,
            password=hashed_password,
            linkedin_url=linkdin_url,
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        logger.info(f"Register user completed with email: {email}")
        if not user:
            logger.info(f"User not created against email: {email}")
        return user
    except Exception as e:
        logger.error(f"Error in register_user: {e}")
        return False


def get_resume(resume_name, resume_folder):
    try:
        logger.info(
            f"Get resume links from s3 Started with resume name: {resume_name} and resume folder: {resume_folder}")
        resume_file = file_manager_object.get_s3_resume(
            AWS_AUTOMATION_RESUME_FOLDER +
            str(resume_folder) + "/" + str(resume_name)
        )
        logger.info(
            f"Get resume links from s3 Completed with resume name: {resume_name} and resume folder: {resume_folder}")
        return resume_file
    except Exception as e:
        logger.error(
            f"Error in getting resume {resume_name} in {resume_folder}: {e}")
        raise e


def add_failed_invite(reason, feedback, score, user_id, db):
    try:
        logger.info("add failed invite init")

        db_failed_invite = FailedInvite(
            user_id=user_id,
            reason=reason,
            feedback=feedback,
            score=score,
        )

        db.add(db_failed_invite)
        db.commit()
        db.refresh(db_failed_invite)

        logger.info(
            f"Inserted failed invite for user_id {user_id} into the database."
        )
    except Exception as e:
        logger.error(f"Error while adding failed invite: {e}")
        raise e

def check_and_upload_resume(user, resume, title, db, request):
    try:
        source_key = AWS_AUTOMATION_RESUME_FOLDER + \
            str(title) + "/" + str(resume)
        destination_key = AWS_RESUME_FOLDER + \
            str(user.id) + "/" + str(resume)
        resume_file = get_resume(resume, title)
        logger.info(f"Resume file: {resume_file}")
        
        file_url, copy_response = file_manager_object.copy_s3_file(
            AWS_BUCKET, source_key, AWS_BUCKET, destination_key, user.id
        )
        logger.info(f"File_url: {file_url}")
        is_resume_upload = upload_resume(
            request, resume, file_url, resume_file, db, user.id
        )
        return is_resume_upload

    except Exception as e:
        logger.error(
            f"Error in getting resume: {e}")
        raise e

def upload_user_into_opensearch(user, db, ):
    try:
        exists = db.query(
            db.query(InterviewFeedback)
            .filter(InterviewFeedback.user_id == user.id)
            .exists()
        ).scalar()

        if not exists:
            logger.info(
                f"User not found in interview feedback, inserting to OpenSearch: {user.id}")
            user_data = interview_service.convert_data(user.id, db)
            response = OpenSearchServiceClass(
                index_name=OPENSEARCH_RC_INDEX).insert_to_opensearch_auto_id(user_data)

            logger.info(f"Opensearch response: {response}")

            update_user = db.query(User).filter(
                User.id == user.id).first()
            update_user.opensearch_status = 1
            db.commit()
            db.refresh(update_user)
            logger.info(
                f"User status updated: {user.id}")
        else:
            logger.info(
                f"User already exists in interview feedback: {user.id}")
    except Exception as e:
        logger.error(f"Error inserting user to OpenSearch: {e}")