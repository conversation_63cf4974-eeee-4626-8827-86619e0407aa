import pytest
from unittest.mock import Mock, MagicMock
from sqlalchemy.orm import Session
from models.skill import Skill
from models.user_skill import User<PERSON>kill


def test_fetch_user_skills():
    """
    Test the skills fetching logic from the interview service.
    This tests the code snippet that fetches skill names for a user
    and generates a skills text string.
    """
    # Arrange
    user_id = 123
    
    # Mock database session
    mock_db = Mock(spec=Session)
    
    # Mock skills data
    mock_skills = [
        <PERSON><PERSON>(name="Python"),
        <PERSON><PERSON>(name="JavaScript"), 
        <PERSON><PERSON>(name="SQL"),
        <PERSON><PERSON>(name="Docker")
    ]
    
    # Mock user skills data
    mock_user_skills = [
        <PERSON><PERSON>(skill_id=1),
        <PERSON><PERSON>(skill_id=2),
        <PERSON><PERSON>(skill_id=3),
        <PERSON><PERSON>(skill_id=4)
    ]
    
    # Configure the mock database queries
    # First query: get user skills for the user
    mock_user_skill_query = Mock()
    mock_user_skill_query.filter.return_value.all.return_value = mock_user_skills
    
    # Second query: get skill names based on skill IDs
    mock_skill_query = Mock()
    mock_skill_query.filter.return_value.all.return_value = mock_skills
    
    # Configure db.query to return appropriate mocks
    def mock_query_side_effect(model):
        if model == UserSkill:
            return mock_user_skill_query
        elif model == Skill:
            return mock_skill_query
        return Mock()
    
    mock_db.query.side_effect = mock_query_side_effect
    
    # Act - Execute the code snippet
    skill_names = [
        skill.name for skill in mock_db.query(Skill)
        .filter(Skill.id.in_(
            [user_skill.skill_id for user_skill in mock_db.query(UserSkill)
            .filter(UserSkill.user_id == user_id).all()]
        )).all()
    ]
    
    # Generate skills embeddings
    skills_text = ", ".join(skill_names)
    
    # Assert
    assert len(skill_names) == 4
    assert "Python" in skill_names
    assert "JavaScript" in skill_names
    assert "SQL" in skill_names
    assert "Docker" in skill_names
    assert skills_text == "Python, JavaScript, SQL, Docker"
    
    # Verify the database queries were called correctly
    mock_db.query.assert_any_call(UserSkill)
    mock_db.query.assert_any_call(Skill)
    mock_user_skill_query.filter.assert_called_once()
    mock_skill_query.filter.assert_called_once()


def test_fetch_user_skills_empty_result():
    """
    Test the skills fetching logic when user has no skills.
    """
    # Arrange
    user_id = 456
    mock_db = Mock(spec=Session)
    
    # Mock empty results
    mock_user_skill_query = Mock()
    mock_user_skill_query.filter.return_value.all.return_value = []
    
    mock_skill_query = Mock()
    mock_skill_query.filter.return_value.all.return_value = []
    
    def mock_query_side_effect(model):
        if model == UserSkill:
            return mock_user_skill_query
        elif model == Skill:
            return mock_skill_query
        return Mock()
    
    mock_db.query.side_effect = mock_query_side_effect
    
    # Act
    skill_names = [
        skill.name for skill in mock_db.query(Skill)
        .filter(Skill.id.in_(
            [user_skill.skill_id for user_skill in mock_db.query(UserSkill)
            .filter(UserSkill.user_id == user_id).all()]
        )).all()
    ]
    
    skills_text = ", ".join(skill_names)
    
    # Assert
    assert len(skill_names) == 0
    assert skills_text == ""


def test_fetch_user_skills_single_skill():
    """
    Test the skills fetching logic when user has only one skill.
    """
    # Arrange
    user_id = 789
    mock_db = Mock(spec=Session)
    
    # Mock single skill
    mock_skills = [Mock(name="React")]
    mock_user_skills = [Mock(skill_id=1)]
    
    mock_user_skill_query = Mock()
    mock_user_skill_query.filter.return_value.all.return_value = mock_user_skills
    
    mock_skill_query = Mock()
    mock_skill_query.filter.return_value.all.return_value = mock_skills
    
    def mock_query_side_effect(model):
        if model == UserSkill:
            return mock_user_skill_query
        elif model == Skill:
            return mock_skill_query
        return Mock()
    
    mock_db.query.side_effect = mock_query_side_effect
    
    # Act
    skill_names = [
        skill.name for skill in mock_db.query(Skill)
        .filter(Skill.id.in_(
            [user_skill.skill_id for user_skill in mock_db.query(UserSkill)
            .filter(UserSkill.user_id == user_id).all()]
        )).all()
    ]
    
    skills_text = ", ".join(skill_names)
    
    # Assert
    assert len(skill_names) == 1
    assert skill_names[0] == "React"
    assert skills_text == "React"


if __name__ == "__main__":
    # Run the tests
    test_fetch_user_skills()
    test_fetch_user_skills_empty_result()
    test_fetch_user_skills_single_skill()
    print("All tests passed!")
